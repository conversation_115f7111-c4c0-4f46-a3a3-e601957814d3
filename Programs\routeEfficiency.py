
# ----- CONSTANTS -----

# ASSUMPTIONS 
BUS_SPEED     = 20
WALKING_SPEED = 3
TRAFFIC_TIME  = 10
COST_PER_MILE = 7.5

# DENSITIES
CITY_PEOPLE_DENSITY   = 3822.1
CITY_BUSINESS_DENSITY = 6.8

SUBURBAN_PEOPLE_DENSITY   = 1587.6
SUBURBAN_BUSINESS_DENSITY = 4

# WEIGHTS
TIME_WEIGHT     = 0.25
PEOPLE_WEIGHT   = 0.6
BUSINESS_WEIGHT = 0.15
COST_WEIGHT     = 0.022


# ----- TIME REDUCTION -----
# ----- T(L) -----


def bus_time(distance):
  rush_weekday = distance / BUS_SPEED + TRAFFIC_TIME / 60
  rush_weekend = (distance + 1) / BUS_SPEED
  non_rush_weekday = distance / BUS_SPEED
  non_rush_weekend = (distance + 1) / BUS_SPEED
  total = rush_weekday + rush_weekend + non_rush_weekday + non_rush_weekend
  return total

def walking_time(distance):
  total = 4 * (distance / WALKING_SPEED)
  return total

def route_cost(distance):
  total = (COST_PER_MILE * COST_WEIGHT) * (1 + 0.28 * distance) * (distance)
  return total

# Time reduction
def T(distance):
  bus = bus_time(distance)
  walk = walking_time(distance)
  cost = route_cost(distance)
  difference = walk - bus - cost
  #return rounded_ratio
  return TIME_WEIGHT * difference


# ----- PEOPLE and BUSINESSES -----
# ----- P(p) -----


def people(city_num, suburban_num):
  max_city = max(routes, key=lambda x: x[2])
  min_districts = min(routes, key=lambda x: x[2]+x[3])[2] + min(routes, key=lambda x: x[2]+x[3])[3]
  total = ((CITY_PEOPLE_DENSITY * city_num) + (SUBURBAN_PEOPLE_DENSITY * suburban_num) - (min_districts * SUBURBAN_PEOPLE_DENSITY)) / ( (max_city[2] * CITY_PEOPLE_DENSITY) + (max_city[3] * SUBURBAN_PEOPLE_DENSITY) - (min_districts * SUBURBAN_PEOPLE_DENSITY) )
  return total

def businesses(city_num, suburban_num):
  max_city = max(routes, key=lambda x: x[2])
  min_districts = min(routes, key=lambda x: x[2]+x[3])[2] + min(routes, key=lambda x: x[2]+x[3])[3]
  total = ((CITY_BUSINESS_DENSITY * city_num) + (SUBURBAN_BUSINESS_DENSITY * suburban_num) - (min_districts * SUBURBAN_BUSINESS_DENSITY)) / ( (max_city[2] * CITY_BUSINESS_DENSITY) + (max_city[3] * SUBURBAN_BUSINESS_DENSITY) - (min_districts * SUBURBAN_BUSINESS_DENSITY) )
  return total

# People and Businesses
def P(city_num, suburban_num):
  people_total = PEOPLE_WEIGHT * people(city_num, suburban_num)
  business_total = BUSINESS_WEIGHT * businesses(city_num, suburban_num)
  return people_total + business_total
  

# ----- FINAL EFFICIENCY -----


def final_efficiency(route):
  name, distance, city_num, suburban_num = route

  total = T(distance) * P(city_num, suburban_num)
  rounded = round(total, 4)

  return name, rounded


# ROUTES:
# Format: [ (str) Name of Route, (float) Distance in miles, (int) Number of city districts, (int) Number of suburban districts ]
routes = [
  ["38th Street / Excelsior",        15.4,   13, 5 ],
  ["46th Street",                    8.5,    9,  2 ],
  ["63rd Avenue / Zane",             6.0,    0,  6 ],
  ["66th Street",                    7.3,    0,  8 ],
  ["Bloomington / Lyndale",          15.4,   18, 4 ],
  ["Broadway",                       8.1,    8,  4 ],
  ["Century",                        15.8,   0,  11],
  ["County Road C",                  9.7,    0,  7 ],
  ["Dale / George",                  9.7,    0,  16],
  ["Franklin / Grand / 3rd Street", 15.9,    10, 15],
  ["Hennepin / Larpenteur",         14.5,    8,  7 ],
  ["Johnson / Lyndale",             17.7,    22, 7 ],
  ["Lowry",                         10.1,    7,  4 ],
  ["Nicollet",                      9.3,     13, 4 ],
  ["North Snelling / Lexington",    9.7,     0,  7 ],
  ["Payne / Westminster",           6.9,     0,  8 ],
  ["Randolph / East 7th Street",    13.4,    2,  18]
]


def conclude():
  global routes
  rankings = []
  print()
  print("RATINGS: ")
  for i in range(len(routes)):
    route = routes[i]
    info = final_efficiency(route)
    rankings.append(info)
    print('='*20)
    print(info[0])
    print("T(L): ", T(route[1]))
    print("P(p): ", P(route[2], route[3]))

  print()
  
  rankings.sort(key=lambda x: x[1], reverse=True)
  print("RANKINGS: ")
  for i in range(len(rankings)):
    print(i + 1, " : ", rankings[i][1], " - ", rankings[i][0])

conclude()
