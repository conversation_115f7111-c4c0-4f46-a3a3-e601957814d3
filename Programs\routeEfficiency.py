
# ----- CONSTANTS -----

# ASSUMPTIONS 
BUS_SPEED     = 20
WALKING_SPEED = 3
TRAFFIC_TIME  = 10
COST_PER_MILE = 7.5

# DENSITIES
CITY_PEOPLE_DENSITY   = 3822.1
CITY_BUSINESS_DENSITY = 6.8

SUBURBAN_PEOPLE_DENSITY   = 1587.6
SUBURBAN_BUSINESS_DENSITY = 4

# WEIGHTS
TIME_WEIGHT     = 1 / 5.13158
PEOPLE_WEIGHT   = 0.6
BUSINESS_WEIGHT = 0.4
COST_WEIGHT     = 0.021


# ----- TIME REDUCTION -----
# ----- T(L) -----


def bus_time(distance):
  rush_weekday = distance / BUS_SPEED + TRAFFIC_TIME / 60
  rush_weekend = (distance + 1) / BUS_SPEED
  non_rush_weekday = distance / BUS_SPEED
  non_rush_weekend = (distance + 1) / BUS_SPEED
  total = rush_weekday + rush_weekend + non_rush_weekday + non_rush_weekend
  return total

def walking_time(distance):
  total = 4 * (distance / WALKING_SPEED)
  return total

def route_cost(distance):
  total = (COST_PER_MILE * COST_WEIGHT) * (1 + 0.28 * distance) * (distance)
  return total

# Time reduction
def T(distance):
  bus = bus_time(distance)
  walk = walking_time(distance)
  cost = route_cost(distance)
  difference = walk - bus - cost
  #return rounded_ratio
  return TIME_WEIGHT * difference


# ----- PEOPLE and BUSINESSES -----
# ----- P(p) -----


def people(city_num, suburban_num):
  max_city = max(routes, key=lambda x: x[2])
  min_districts = min(routes, key=lambda x: x[2]+x[3])[2] + min(routes, key=lambda x: x[2]+x[3])[3]
  total = ((CITY_PEOPLE_DENSITY * city_num) + (SUBURBAN_PEOPLE_DENSITY * suburban_num) - (min_districts * SUBURBAN_PEOPLE_DENSITY)) / ( (max_city[2] * CITY_PEOPLE_DENSITY) + (max_city[3] * SUBURBAN_PEOPLE_DENSITY) - (min_districts * SUBURBAN_PEOPLE_DENSITY) )
  return total

def businesses(city_num, suburban_num):
  max_city = max(routes, key=lambda x: x[2])
  min_districts = min(routes, key=lambda x: x[2]+x[3])[2] + min(routes, key=lambda x: x[2]+x[3])[3]
  total = ((CITY_BUSINESS_DENSITY * city_num) + (SUBURBAN_BUSINESS_DENSITY * suburban_num) - (min_districts * SUBURBAN_BUSINESS_DENSITY)) / ( (max_city[2] * CITY_BUSINESS_DENSITY) + (max_city[3] * SUBURBAN_BUSINESS_DENSITY) - (min_districts * SUBURBAN_BUSINESS_DENSITY) )
  return total

# People and Businesses
def P(city_num, suburban_num):
  people_total = PEOPLE_WEIGHT * people(city_num, suburban_num)
  business_total = BUSINESS_WEIGHT * businesses(city_num, suburban_num)
  return people_total + business_total
  

# ----- FINAL EFFICIENCY -----


def final_efficiency(route):
  name, distance, city_num, suburban_num = route

  total = T(distance) * P(city_num, suburban_num)
  rounded = round(total, 4)

  return name, rounded


# ROUTES:
# Format: [ (str) Name of Route, (float) Distance in miles, (int) Number of city districts, (int) Number of suburban districts ]
routes = [
  ["38th Street / Excelsior",        15.4,   13, 5 ],
  ["46th Street",                    8.5,    9,  2 ],
  ["63rd Avenue / Zane",             6.0,    0,  6 ],
  ["66th Street",                    7.3,    0,  8 ],
  ["Bloomington / Lyndale",          15.4,   18, 4 ],
  ["Broadway",                       8.1,    8,  4 ],
  ["Century",                        15.8,   0,  11],
  ["County Road C",                  9.7,    0,  7 ],
  ["Dale / George",                  9.7,    0,  16],
  ["Franklin / Grand / 3rd Street", 15.9,    10, 15],
  ["Hennepin / Larpenteur",         14.5,    8,  7 ],
  ["Johnson / Lyndale",             17.7,    22, 7 ],
  ["Lowry",                         10.1,    7,  4 ],
  ["Nicollet",                      9.3,     13, 4 ],
  ["North Snelling / Lexington",    9.7,     0,  7 ],
  ["Payne / Westminster",           6.9,     0,  8 ],
  ["Randolph / East 7th Street",    13.4,    2,  18]
]


def conclude():
  global routes
  rankings = []
  route_data = []  # Store all data for graphing

  print()
  print("RATINGS: ")
  for i in range(len(routes)):
    route = routes[i]
    info = final_efficiency(route)
    rankings.append(info)

    # Store data for graphing
    route_data.append({
      'name': info[0],
      'efficiency': info[1],
      'time_reduction': T(route[1]),
      'people_business': P(route[2], route[3]),
      'distance': route[1],
      'city_districts': route[2],
      'suburban_districts': route[3]
    })

    print('='*20)
    print(info[0])
    print("T(L): ", T(route[1]))
    print("P(p): ", P(route[2], route[3]))

  print()

  rankings.sort(key=lambda x: x[1], reverse=True)
  print("RANKINGS: ")
  for i in range(len(rankings)):
    print(i + 1, " : ", rankings[i][1], " - ", rankings[i][0])

  # Generate graphs
  create_graphs(route_data)


def create_graphs(route_data):
  """Create multiple visualizations of the route efficiency data"""
  import matplotlib.pyplot as plt
  import numpy as np

  # Sort data by efficiency for consistent ordering
  sorted_data = sorted(route_data, key=lambda x: x['efficiency'], reverse=True)

  # Extract data for plotting
  names = [d['name'] for d in sorted_data]
  efficiencies = [d['efficiency'] for d in sorted_data]
  time_reductions = [d['time_reduction'] for d in sorted_data]
  people_business = [d['people_business'] for d in sorted_data]
  distances = [d['distance'] for d in sorted_data]

  # Create figure with multiple subplots
  fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
  fig.suptitle('Route Efficiency Analysis', fontsize=16, fontweight='bold')

  # 1. Bar chart of final efficiency rankings
  colors = plt.cm.viridis(np.linspace(0, 1, len(names)))
  bars1 = ax1.bar(range(len(names)), efficiencies, color=colors)
  ax1.set_title('Final Efficiency Rankings')
  ax1.set_xlabel('Routes (Ranked by Efficiency)')
  ax1.set_ylabel('Efficiency Score')
  ax1.set_xticks(range(len(names)))
  ax1.set_xticklabels([name[:15] + '...' if len(name) > 15 else name for name in names],
                      rotation=45, ha='right')

  # Add value labels on bars
  for i, bar in enumerate(bars1):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
             f'{height:.3f}', ha='center', va='bottom', fontsize=8)

  # 2. Scatter plot: Time Reduction vs People/Business
  scatter = ax2.scatter(time_reductions, people_business,
                       c=efficiencies, s=100, cmap='viridis', alpha=0.7)
  ax2.set_title('Time Reduction vs People/Business Factor')
  ax2.set_xlabel('Time Reduction T(L)')
  ax2.set_ylabel('People/Business Factor P(p)')

  # Add colorbar for efficiency
  cbar = plt.colorbar(scatter, ax=ax2)
  cbar.set_label('Efficiency Score')

  # Add route labels to points
  for i, name in enumerate(names):
    ax2.annotate(name[:10] + '...' if len(name) > 10 else name,
                (time_reductions[i], people_business[i]),
                xytext=(5, 5), textcoords='offset points', fontsize=8, alpha=0.7)

  # 3. Distance vs Efficiency
  ax3.scatter(distances, efficiencies, c=colors, s=100, alpha=0.7)
  ax3.set_title('Route Distance vs Efficiency')
  ax3.set_xlabel('Distance (miles)')
  ax3.set_ylabel('Efficiency Score')

  # Add trend line
  z = np.polyfit(distances, efficiencies, 1)
  p = np.poly1d(z)
  ax3.plot(distances, p(distances), "r--", alpha=0.8, linewidth=2)

  # 4. Component comparison (T(L) and P(p))
  x_pos = np.arange(len(names))
  width = 0.35

  bars_t = ax4.bar(x_pos - width/2, time_reductions, width,
                   label='Time Reduction T(L)', alpha=0.8, color='skyblue')
  bars_p = ax4.bar(x_pos + width/2, people_business, width,
                   label='People/Business P(p)', alpha=0.8, color='lightcoral')

  ax4.set_title('Component Comparison: T(L) vs P(p)')
  ax4.set_xlabel('Routes')
  ax4.set_ylabel('Score')
  ax4.set_xticks(x_pos)
  ax4.set_xticklabels([name[:10] + '...' if len(name) > 10 else name for name in names],
                      rotation=45, ha='right')
  ax4.legend()

  plt.tight_layout()
  plt.show()

  # Create a separate detailed ranking chart
  create_ranking_chart(sorted_data)

def create_ranking_chart(sorted_data):
  """Create a detailed horizontal bar chart showing rankings"""
  import matplotlib.pyplot as plt
  import numpy as np

  names = [d['name'] for d in sorted_data]
  efficiencies = [d['efficiency'] for d in sorted_data]

  # Create horizontal bar chart
  fig, ax = plt.subplots(figsize=(12, 10))

  # Create color gradient
  colors = plt.cm.RdYlGn(np.linspace(0.3, 1, len(names)))

  bars = ax.barh(range(len(names)), efficiencies, color=colors)

  ax.set_title('Route Efficiency Rankings (Detailed)', fontsize=16, fontweight='bold', pad=20)
  ax.set_xlabel('Efficiency Score', fontsize=12)
  ax.set_ylabel('Routes', fontsize=12)
  ax.set_yticks(range(len(names)))
  ax.set_yticklabels([f"{i+1}. {name}" for i, name in enumerate(names)])

  # Add value labels
  for i, bar in enumerate(bars):
    width = bar.get_width()
    ax.text(width + 0.01, bar.get_y() + bar.get_height()/2,
            f'{width:.4f}', ha='left', va='center', fontweight='bold')

  # Invert y-axis so rank 1 is at top
  ax.invert_yaxis()

  # Add grid for better readability
  ax.grid(axis='x', alpha=0.3)
  ax.set_axisbelow(True)

  plt.tight_layout()
  plt.show()

conclude()
